;; Rewards contract for GreenTrack platform
;; Distributes GreenPoints governance tokens and manages reward schedules

;; Error constants
(define-constant ERR_UNAUTHORIZED (err u9001))
(define-constant ERR_INVALID_REWARD (err u9002))
(define-constant ERR_ALREADY_CLAIMED (err u9003))
(define-constant ERR_REWARD_EXPIRED (err u9004))

;; Reward constants
(define-constant GREENPOINTS_PER_CREDIT u10) ;; 10 GP per carbon credit issued
(define-constant TRADING_REWARD_RATE u1) ;; 1 GP per 1000 CCT traded
(define-constant STAKING_BONUS_RATE u5) ;; 5% bonus for stakers

;; Data structures
(define-map user-rewards principal {
    total-earned: uint,
    total-claimed: uint,
    last-claim-height: uint
})

(define-map reward-campaigns uint {
    name: (string-ascii 64),
    reward-per-action: uint,
    start-height: uint,
    end-height: uint,
    total-budget: uint,
    claimed-amount: uint,
    is-active: bool
})

(define-map campaign-participants { campaign-id: uint, user: principal } {
    actions-completed: uint,
    rewards-earned: uint,
    claimed: bool
})

;; Data variables
(define-data-var next-campaign-id uint u1)
(define-data-var total-greenpoints-distributed uint u0)
(define-data-var contract-admin principal tx-sender)

;; References
(define-constant CARBON_TOKEN_CONTRACT .carbon-token)

;; Reward users for issuing carbon credits
(define-public (reward-credit-issuance (issuer principal) (credits-count uint))
    (begin
        ;; Only credit registry can call this
        (asserts! (is-eq contract-caller .credit-registry) ERR_UNAUTHORIZED)

        (let ((reward-amount (* credits-count GREENPOINTS_PER_CREDIT)))
            ;; Update user rewards
            (match (map-get? user-rewards issuer)
                existing-rewards
                    (map-set user-rewards issuer (merge existing-rewards {
                        total-earned: (+ (get total-earned existing-rewards) reward-amount)
                    }))
                (map-set user-rewards issuer {
                    total-earned: reward-amount,
                    total-claimed: u0,
                    last-claim-height: block-height
                }))

            ;; Update total distributed
            (var-set total-greenpoints-distributed
                (+ (var-get total-greenpoints-distributed) reward-amount))

            (print {
                type: "issuance-reward",
                issuer: issuer,
                credits-count: credits-count,
                reward-amount: reward-amount
            })
            (ok reward-amount))))

;; Reward users for marketplace trading
(define-public (reward-trading-activity (trader principal) (volume uint))
    (begin
        ;; Only marketplace can call this
        (asserts! (is-eq contract-caller .marketplace) ERR_UNAUTHORIZED)

        (let ((reward-amount (/ volume u1000))) ;; 1 GP per 1000 volume
            (if (> reward-amount u0)
                (begin
                    ;; Update user rewards
                    (match (map-get? user-rewards trader)
                        existing-rewards
                            (map-set user-rewards trader (merge existing-rewards {
                                total-earned: (+ (get total-earned existing-rewards) reward-amount)
                            }))
                        (map-set user-rewards trader {
                            total-earned: reward-amount,
                            total-claimed: u0,
                            last-claim-height: block-height
                        }))

                    (var-set total-greenpoints-distributed
                        (+ (var-get total-greenpoints-distributed) reward-amount))

                    (print {
                        type: "trading-reward",
                        trader: trader,
                        volume: volume,
                        reward-amount: reward-amount
                    })
                    (ok reward-amount))
                (ok u0)))))

;; Create reward campaign
(define-public (create-campaign
    (name (string-ascii 64))
    (reward-per-action uint)
    (duration-blocks uint)
    (total-budget uint))
    (let ((campaign-id (var-get next-campaign-id)))
        (asserts! (is-eq tx-sender (var-get contract-admin)) ERR_UNAUTHORIZED)
        (asserts! (> reward-per-action u0) ERR_INVALID_REWARD)
        (asserts! (> duration-blocks u0) ERR_INVALID_REWARD)
        (asserts! (> total-budget u0) ERR_INVALID_REWARD)

        (map-set reward-campaigns campaign-id {
            name: name,
            reward-per-action: reward-per-action,
            start-height: block-height,
            end-height: (+ block-height duration-blocks),
            total-budget: total-budget,
            claimed-amount: u0,
            is-active: true
        })

        (var-set next-campaign-id (+ campaign-id u1))
        (ok campaign-id)))

;; Claim accumulated rewards
(define-public (claim-rewards)
    (match (map-get? user-rewards tx-sender)
        reward-data
            (let ((claimable-amount (- (get total-earned reward-data) (get total-claimed reward-data))))
                (asserts! (> claimable-amount u0) ERR_INVALID_REWARD)

                ;; Mint GreenPoints tokens
                (try! (as-contract (contract-call? CARBON_TOKEN_CONTRACT mint claimable-amount tx-sender)))

                ;; Update claimed amount
                (map-set user-rewards tx-sender (merge reward-data {
                    total-claimed: (get total-earned reward-data),
                    last-claim-height: block-height
                }))

                (print {
                    type: "rewards-claimed",
                    user: tx-sender,
                    amount: claimable-amount
                })
                (ok claimable-amount))
        ERR_INVALID_REWARD))

;; Admin function to update reward rates
(define-public (update-admin (new-admin principal))
    (begin
        (asserts! (is-eq tx-sender (var-get contract-admin)) ERR_UNAUTHORIZED)
        (var-set contract-admin new-admin)
        (ok true)))

;; Read-only functions
(define-read-only (get-user-rewards (user principal))
    (map-get? user-rewards user))

(define-read-only (get-claimable-rewards (user principal))
    (match (map-get? user-rewards user)
        reward-data (- (get total-earned reward-data) (get total-claimed reward-data))
        u0))

(define-read-only (get-campaign-info (campaign-id uint))
    (map-get? reward-campaigns campaign-id))

(define-read-only (get-rewards-stats)
    {
        total-distributed: (var-get total-greenpoints-distributed),
        greenpoints-per-credit: GREENPOINTS_PER_CREDIT,
        trading-reward-rate: TRADING_REWARD_RATE,
        active-campaigns: (- (var-get next-campaign-id) u1)
    })

(define-read-only (is-campaign-active (campaign-id uint))
    (match (map-get? reward-campaigns campaign-id)
        campaign-data (and (get is-active campaign-data)
                          (<= block-height (get end-height campaign-data))
                          (>= block-height (get start-height campaign-data)))
        false))