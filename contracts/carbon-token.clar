;; Carbon Credit Token (CCT) - ERC-20 style token for GreenTrack platform
;; Represents tokenized carbon credits that can be traded on the marketplace

(use-trait math-trait 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.utils-math.math-trait)

;; Error constants
(define-constant ERR_INSUFFICIENT_BALANCE (err u4001))
(define-constant ERR_INVALID_RECIPIENT (err u4002))
(define-constant ERR_UNAUTHORIZED (err u4003))
(define-constant ERR_INVALID_AMOUNT (err u4004))

;; Token constants
(define-constant TOKEN_NAME "Carbon Credit Token")
(define-constant TOKEN_SYMBOL "CCT")
(define-constant TOKEN_DECIMALS u6)
(define-constant TOKEN_URI "https://greentrack.org/metadata/cct")

;; Contract deployer as initial admin
(define-constant CONTRACT_OWNER tx-sender)

;; Data variables
(define-data-var total-supply uint u0)
(define-data-var token-uri (string-ascii 256) TOKEN_URI)

;; Data maps
(define-map balances principal uint)
(define-map allowances { owner: principal, spender: principal } uint)
(define-map authorized-minters principal bool)

;; Initialize authorized minters
(map-set authorized-minters CONTRACT_OWNER true)

;; SIP-010 trait implementation
(define-public (transfer (amount uint) (sender principal) (recipient principal) (memo (optional (buff 34))))
    (begin
        (asserts! (is-eq tx-sender sender) ERR_UNAUTHORIZED)
        (asserts! (not (is-eq sender recipient)) ERR_INVALID_RECIPIENT)
        (asserts! (> amount u0) ERR_INVALID_AMOUNT)
        (let ((sender-balance (get-balance sender)))
            (asserts! (>= sender-balance amount) ERR_INSUFFICIENT_BALANCE)
            (map-set balances sender (- sender-balance amount))
            (map-set balances recipient (+ (get-balance recipient) amount))
            (print {
                type: "transfer",
                sender: sender,
                recipient: recipient,
                amount: amount,
                memo: memo
            })
            (ok true))))

(define-public (mint (amount uint) (recipient principal))
    (begin
        (asserts! (default-to false (map-get? authorized-minters tx-sender)) ERR_UNAUTHORIZED)
        (asserts! (> amount u0) ERR_INVALID_AMOUNT)
        (let ((recipient-balance (get-balance recipient))
              (new-total-supply (+ (var-get total-supply) amount)))
            (var-set total-supply new-total-supply)
            (map-set balances recipient (+ recipient-balance amount))
            (print {
                type: "mint",
                recipient: recipient,
                amount: amount
            })
            (ok true))))

(define-public (burn (amount uint) (owner principal))
    (begin
        (asserts! (or (is-eq tx-sender owner)
                      (default-to false (map-get? authorized-minters tx-sender))) ERR_UNAUTHORIZED)
        (asserts! (> amount u0) ERR_INVALID_AMOUNT)
        (let ((owner-balance (get-balance owner)))
            (asserts! (>= owner-balance amount) ERR_INSUFFICIENT_BALANCE)
            (map-set balances owner (- owner-balance amount))
            (var-set total-supply (- (var-get total-supply) amount))
            (print {
                type: "burn",
                owner: owner,
                amount: amount
            })
            (ok true))))

(define-public (add-authorized-minter (minter principal))
    (begin
        (asserts! (is-eq tx-sender CONTRACT_OWNER) ERR_UNAUTHORIZED)
        (ok (map-set authorized-minters minter true))))

(define-public (remove-authorized-minter (minter principal))
    (begin
        (asserts! (is-eq tx-sender CONTRACT_OWNER) ERR_UNAUTHORIZED)
        (ok (map-delete authorized-minters minter))))

;; Read-only functions
(define-read-only (get-name)
    (ok TOKEN_NAME))

(define-read-only (get-symbol)
    (ok TOKEN_SYMBOL))

(define-read-only (get-decimals)
    (ok TOKEN_DECIMALS))

(define-read-only (get-balance (owner principal))
    (default-to u0 (map-get? balances owner)))

(define-read-only (get-total-supply)
    (ok (var-get total-supply)))

(define-read-only (get-token-uri)
    (ok (some (var-get token-uri))))

(define-read-only (is-authorized-minter (minter principal))
    (default-to false (map-get? authorized-minters minter)))