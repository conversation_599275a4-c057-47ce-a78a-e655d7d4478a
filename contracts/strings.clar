;; String utility functions for GreenTrack platform
;; Provides string validation and formatting helpers

;; Constants
(define-constant ERR_INVALID_STRING (err u2001))
(define-constant ERR_STRING_TOO_LONG (err u2002))
(define-constant MAX_STRING_LENGTH u64)

;; Validate string length
(define-public (validate-string-length (str (string-ascii 64)))
    (let ((length (len str)))
        (if (<= length MAX_STRING_LENGTH)
            (ok true)
            ERR_STRING_TOO_LONG)))

;; Check if string is empty
(define-public (is-empty-string (str (string-ascii 64)))
    (ok (is-eq (len str) u0)))

;; Validate credit ID format (must be non-empty and within limits)
(define-public (validate-credit-id (id (string-ascii 32)))
    (if (and (> (len id) u0) (<= (len id) u32))
        (ok true)
        ERR_INVALID_STRING))

;; Create formatted project identifier
(define-public (format-project-id (project-type (string-ascii 16)) (sequence uint))
    (if (> (len project-type) u16)
        ERR_STRING_TOO_LONG
        (ok (concat project-type "-"))))

;; Validate metadata format
(define-public (validate-metadata (metadata (string-ascii 256)))
    (if (<= (len metadata) u256)
        (ok true)
        ERR_STRING_TOO_LONG))