;; Math utility functions for GreenTrack platform
;; Provides safe arithmetic operations and percentage calculations

;; Constants
(define-constant ERR_DIVISION_BY_ZERO (err u1001))
(define-constant ERR_OVERFLOW (err u1002))
(define-constant MAX_UINT u340282366920938463463374607431768211455)

;; Safe multiplication with overflow check
(define-public (safe-multiply (a uint) (b uint))
    (let ((result (* a b)))
        (if (and (> a u0) (> (/ MAX_UINT a) b))
            (err ERR_OVERFLOW)
            (ok result))))

;; Safe addition with overflow check
(define-public (safe-add (a uint) (b uint))
    (let ((result (+ a b)))
        (if (< result a)
            (err ERR_OVERFLOW)
            (ok result))))

;; Safe subtraction with underflow check
(define-public (safe-subtract (a uint) (b uint))
    (if (>= a b)
        (ok (- a b))
        (err u1003)))

;; Calculate percentage with precision
(define-public (calculate-percentage (amount uint) (percentage uint))
    (if (is-eq percentage u0)
        (ok u0)
        (safe-multiply amount (/ percentage u100))))

;; Calculate weighted average
(define-public (weighted-average (value1 uint) (weight1 uint) (value2 uint) (weight2 uint))
    (let ((total-weight (+ weight1 weight2)))
        (if (is-eq total-weight u0)
            ERR_DIVISION_BY_ZERO
            (ok (/ (+ (* value1 weight1) (* value2 weight2)) total-weight)))))

;; Find minimum of two values
(define-public (min (a uint) (b uint))
    (ok (if (<= a b) a b)))

;; Find maximum of two values
(define-public (max (a uint) (b uint))
    (ok (if (>= a b) a b)))