;; Credit Registry - Core registry for carbon credits with metadata and verification
;; Handles issuance, verification, and tracking of carbon credits

;; Error constants
(define-constant ERR_UNAUTHORIZED_ISSUER (err u5001))
(define-constant ERR_CREDIT_ALREADY_EXISTS (err u5002))
(define-constant ERR_CREDIT_NOT_FOUND (err u5003))
(define-constant ERR_INVALID_METADATA (err u5004))
(define-constant ERR_CREDIT_RETIRED (err u5005))

;; Credit status constants
(define-constant STATUS_ACTIVE u1)
(define-constant STATUS_RETIRED u2)
(define-constant STATUS_CANCELLED u3)

;; Data structures
(define-map credits (string-ascii 32) {
    issuer: principal,
    project-id: (string-ascii 64),
    vintage-year: uint,
    methodology: (string-ascii 64),
    co2-amount: uint,
    status: uint,
    issue-date: uint,
    metadata-uri: (string-ascii 256),
    verification-standard: (string-ascii 32)
})

(define-map project-credits (string-ascii 64) (list 100 (string-ascii 32)))
(define-map issuer-credits principal (list 1000 (string-ascii 32)))

;; Data variables
(define-data-var total-credits-issued uint u0)
(define-data-var total-co2-registered uint u0)

;; Access control reference
(define-constant ACCESS_CONTROL_CONTRACT .access-control)

;; Issue new carbon credit
(define-public (issue-credit
    (credit-id (string-ascii 32))
    (project-id (string-ascii 64))
    (vintage-year uint)
    (methodology (string-ascii 64))
    (co2-amount uint)
    (metadata-uri (string-ascii 256))
    (verification-standard (string-ascii 32)))
    (begin
        ;; Verify issuer authorization
        (asserts! (contract-call? ACCESS_CONTROL_CONTRACT is-authorized-issuer tx-sender) ERR_UNAUTHORIZED_ISSUER)
        ;; Check credit doesn't already exist
        (asserts! (is-none (map-get? credits credit-id)) ERR_CREDIT_ALREADY_EXISTS)
        ;; Validate inputs
        (asserts! (> co2-amount u0) ERR_INVALID_METADATA)
        (asserts! (> vintage-year u1990) ERR_INVALID_METADATA)

        ;; Create credit record
        (map-set credits credit-id {
            issuer: tx-sender,
            project-id: project-id,
            vintage-year: vintage-year,
            methodology: methodology,
            co2-amount: co2-amount,
            status: STATUS_ACTIVE,
            issue-date: stacks-block-height,
            metadata-uri: metadata-uri,
            verification-standard: verification-standard
        })

        ;; Update tracking maps
        (let ((existing-project-credits (default-to (list) (map-get? project-credits project-id)))
              (existing-issuer-credits (default-to (list) (map-get? issuer-credits tx-sender))))
            (map-set project-credits project-id (unwrap-panic (as-max-len? (append existing-project-credits credit-id) u100)))
            (map-set issuer-credits tx-sender (unwrap-panic (as-max-len? (append existing-issuer-credits credit-id) u1000))))

        ;; Update counters
        (var-set total-credits-issued (+ (var-get total-credits-issued) u1))
        (var-set total-co2-registered (+ (var-get total-co2-registered) co2-amount))

        (print {
            type: "credit-issued",
            credit-id: credit-id,
            issuer: tx-sender,
            co2-amount: co2-amount,
            project-id: project-id
        })
        (ok credit-id)))

;; Retire carbon credit
(define-public (retire-credit (credit-id (string-ascii 32)) (retiree principal))
    (match (map-get? credits credit-id)
        credit-data
            (begin
                (asserts! (is-eq (get status credit-data) STATUS_ACTIVE) ERR_CREDIT_RETIRED)
                (map-set credits credit-id (merge credit-data { status: STATUS_RETIRED }))
                (print {
                    type: "credit-retired",
                    credit-id: credit-id,
                    retiree: retiree,
                    co2-amount: (get co2-amount credit-data)
                })
                (ok true))
        ERR_CREDIT_NOT_FOUND))

;; Read-only functions
(define-read-only (get-credit-info (credit-id (string-ascii 32)))
    (map-get? credits credit-id))

(define-read-only (get-credits-by-project (project-id (string-ascii 64)))
    (map-get? project-credits project-id))

(define-read-only (get-credits-by-issuer (issuer principal))
    (map-get? issuer-credits issuer))

(define-read-only (get-registry-stats)
    {
        total-credits: (var-get total-credits-issued),
        total-co2-registered: (var-get total-co2-registered)
    })

(define-read-only (is-credit-active (credit-id (string-ascii 32)))
    (match (map-get? credits credit-id)
        credit-data (is-eq (get status credit-data) STATUS_ACTIVE)
        false))