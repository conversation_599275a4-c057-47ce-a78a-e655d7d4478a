;; Marketplace contract for trading carbon credits
;; Handles listings, offers, and atomic swaps with escrow functionality

(use-trait carbon-token-trait 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.carbon-token.sip-010-trait)
(use-trait math-trait 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.utils-math.math-trait)

;; Error constants
(define-constant ERR_LISTING_NOT_FOUND (err u6001))
(define-constant ERR_UNAUTHORIZED (err u6002))
(define-constant ERR_INSUFFICIENT_PAYMENT (err u6003))
(define-constant ERR_INVALID_PRICE (err u6004))
(define-constant ERR_LISTING_EXPIRED (err u6005))
(define-constant ERR_SELF_PURCHASE (err u6006))

;; Platform fee (2%)
(define-constant PLATFORM_FEE_RATE u2)

;; Data structures
(define-map listings uint {
    seller: principal,
    credit-id: (string-ascii 32),
    price-per-credit: uint,
    quantity-available: uint,
    expiry-height: uint,
    is-active: bool
})

(define-map offers uint {
    buyer: principal,
    listing-id: uint,
    price-offered: uint,
    quantity-requested: uint,
    offer-expiry: uint,
    is-active: bool
})

;; Data variables
(define-data-var next-listing-id uint u1)
(define-data-var next-offer-id uint u1)
(define-data-var platform-treasury principal tx-sender)
(define-data-var total-volume uint u0)

;; References
(define-constant CARBON_TOKEN_CONTRACT 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.carbon-token)
(define-constant CREDIT_REGISTRY_CONTRACT 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.credit-registry)

;; Create marketplace listing
(define-public (create-listing
    (credit-id (string-ascii 32))
    (price-per-credit uint)
    (quantity uint)
    (expiry-blocks uint))
    (let ((listing-id (var-get next-listing-id)))
        (asserts! (> price-per-credit u0) ERR_INVALID_PRICE)
        (asserts! (> quantity u0) ERR_INVALID_PRICE)
        (asserts! (> expiry-blocks u0) ERR_INVALID_PRICE)

        ;; Verify credit exists and is active
        (asserts! (contract-call? CREDIT_REGISTRY_CONTRACT is-credit-active credit-id) ERR_LISTING_NOT_FOUND)

        ;; Create listing
        (map-set listings listing-id {
            seller: tx-sender,
            credit-id: credit-id,
            price-per-credit: price-per-credit,
            quantity-available: quantity,
            expiry-height: (+ block-height expiry-blocks),
            is-active: true
        })

        (var-set next-listing-id (+ listing-id u1))

        (print {
            type: "listing-created",
            listing-id: listing-id,
            seller: tx-sender,
            credit-id: credit-id,
            price: price-per-credit,
            quantity: quantity
        })
        (ok listing-id)))

;; Purchase credits from listing
(define-public (purchase-credits (listing-id uint) (quantity uint))
    (match (map-get? listings listing-id)
        listing-data
            (let ((total-cost (* (get price-per-credit listing-data) quantity))
                  (platform-fee (/ (* total-cost PLATFORM_FEE_RATE) u100))
                  (seller-amount (- total-cost platform-fee)))
                ;; Validations
                (asserts! (get is-active listing-data) ERR_LISTING_NOT_FOUND)
                (asserts! (<= block-height (get expiry-height listing-data)) ERR_LISTING_EXPIRED)
                (asserts! (not (is-eq tx-sender (get seller listing-data))) ERR_SELF_PURCHASE)
                (asserts! (<= quantity (get quantity-available listing-data)) ERR_INSUFFICIENT_PAYMENT)
                (asserts! (>= (stx-get-balance tx-sender) total-cost) ERR_INSUFFICIENT_PAYMENT)

                ;; Execute payment
                (try! (stx-transfer? seller-amount tx-sender (get seller listing-data)))
                (try! (stx-transfer? platform-fee tx-sender (var-get platform-treasury)))

                ;; Update listing
                (let ((new-quantity (- (get quantity-available listing-data) quantity)))
                    (if (is-eq new-quantity u0)
                        (map-set listings listing-id (merge listing-data {
                            is-active: false,
                            quantity-available: u0
                        }))
                        (map-set listings listing-id (merge listing-data {
                            quantity-available: new-quantity
                        }))))

                ;; Update volume tracking
                (var-set total-volume (+ (var-get total-volume) total-cost))

                (print {
                    type: "purchase-completed",
                    listing-id: listing-id,
                    buyer: tx-sender,
                    seller: (get seller listing-data),
                    quantity: quantity,
                    total-cost: total-cost,
                    platform-fee: platform-fee
                })
                (ok true))
        ERR_LISTING_NOT_FOUND))

;; Cancel listing
(define-public (cancel-listing (listing-id uint))
    (match (map-get? listings listing-id)
        listing-data
            (begin
                (asserts! (is-eq tx-sender (get seller listing-data)) ERR_UNAUTHORIZED)
                (asserts! (get is-active listing-data) ERR_LISTING_NOT_FOUND)
                (map-set listings listing-id (merge listing-data { is-active: false }))
                (print { type: "listing-cancelled", listing-id: listing-id })
                (ok true))
        ERR_LISTING_NOT_FOUND))

;; Create offer for listing
(define-public (create-offer
    (listing-id uint)
    (price-offered uint)
    (quantity-requested uint)
    (expiry-blocks uint))
    (let ((offer-id (var-get next-offer-id)))
        (asserts! (> price-offered u0) ERR_INVALID_PRICE)
        (asserts! (> quantity-requested u0) ERR_INVALID_PRICE)
        (asserts! (is-some (map-get? listings listing-id)) ERR_LISTING_NOT_FOUND)

        (map-set offers offer-id {
            buyer: tx-sender,
            listing-id: listing-id,
            price-offered: price-offered,
            quantity-requested: quantity-requested,
            offer-expiry: (+ block-height expiry-blocks),
            is-active: true
        })

        (var-set next-offer-id (+ offer-id u1))
        (ok offer-id)))

;; Read-only functions
(define-read-only (get-listing (listing-id uint))
    (map-get? listings listing-id))

(define-read-only (get-offer (offer-id uint))
    (map-get? offers offer-id))

(define-read-only (get-marketplace-stats)
    {
        total-volume: (var-get total-volume),
        active-listings-count: u0,
        platform-fee-rate: PLATFORM_FEE_RATE
    })

(define-read-only (is-listing-active (listing-id uint))
    (match (map-get? listings listing-id)
        listing-data (and (get is-active listing-data)
                         (<= block-height (get expiry-height listing-data)))
        false))