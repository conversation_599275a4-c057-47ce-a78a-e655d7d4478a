;; Staking contract for GreenTrack platform
;; Users stake CCT tokens to earn protocol fees and governance rewards

(use-trait carbon-token-trait 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.carbon-token.sip-010-trait)
(use-trait math-trait 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.utils-math.math-trait)

;; Error constants
(define-constant ERR_INSUFFICIENT_STAKE (err u7001))
(define-constant ERR_NO_STAKE_FOUND (err u7002))
(define-constant ERR_COOLDOWN_ACTIVE (err u7003))
(define-constant ERR_INVALID_AMOUNT (err u7004))

;; Staking parameters
(define-constant MIN_STAKE_AMOUNT u1000000) ;; 1 CCT minimum
(define-constant COOLDOWN_PERIOD u144) ;; ~1 day in blocks
(define-constant REWARD_RATE u5) ;; 5% annual base rate

;; Data structures
(define-map stakes principal {
    amount: uint,
    stake-height: uint,
    last-claim-height: uint,
    cooldown-end: (optional uint)
})

(define-map stake-rewards principal uint)
(define-map protocol-fees-distributed uint uint)

;; Data variables
(define-data-var total-staked uint u0)
(define-data-var total-rewards-distributed uint u0)
(define-data-var fee-pool uint u0)

;; References
(define-constant CARBON_TOKEN_CONTRACT 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.carbon-token)

;; Stake CCT tokens
(define-public (stake-tokens (amount uint))
    (begin
        (asserts! (>= amount MIN_STAKE_AMOUNT) ERR_INVALID_AMOUNT)

        ;; Transfer tokens to this contract
        (try! (contract-call? CARBON_TOKEN_CONTRACT transfer amount tx-sender (as-contract tx-sender) none))

        ;; Update or create stake
        (match (map-get? stakes tx-sender)
            existing-stake
                (let ((new-amount (+ (get amount existing-stake) amount)))
                    (map-set stakes tx-sender (merge existing-stake { amount: new-amount })))
            ;; Create new stake
            (map-set stakes tx-sender {
                amount: amount,
                stake-height: block-height,
                last-claim-height: block-height,
                cooldown-end: none
            }))

        ;; Update total staked
        (var-set total-staked (+ (var-get total-staked) amount))

        (print {
            type: "tokens-staked",
            staker: tx-sender,
            amount: amount,
            total-staked: (var-get total-staked)
        })
        (ok amount)))

;; Begin unstaking process (starts cooldown)
(define-public (begin-unstake (amount uint))
    (match (map-get? stakes tx-sender)
        stake-data
            (begin
                (asserts! (>= (get amount stake-data) amount) ERR_INSUFFICIENT_STAKE)
                (asserts! (is-none (get cooldown-end stake-data)) ERR_COOLDOWN_ACTIVE)

                ;; Claim any pending rewards first
                (try! (claim-rewards))

                ;; Set cooldown period
                (map-set stakes tx-sender (merge stake-data {
                    cooldown-end: (some (+ block-height COOLDOWN_PERIOD))
                }))

                (print {
                    type: "unstake-initiated",
                    staker: tx-sender,
                    amount: amount,
                    cooldown-end: (+ block-height COOLDOWN_PERIOD)
                })
                (ok true))
        ERR_NO_STAKE_FOUND))

;; Complete unstaking after cooldown
(define-public (complete-unstake)
    (match (map-get? stakes tx-sender)
        stake-data
            (match (get cooldown-end stake-data)
                cooldown-height
                    (begin
                        (asserts! (<= cooldown-height block-height) ERR_COOLDOWN_ACTIVE)

                        ;; Transfer tokens back to user
                        (try! (as-contract (contract-call? CARBON_TOKEN_CONTRACT transfer
                            (get amount stake-data) tx-sender tx-sender none)))

                        ;; Update total staked
                        (var-set total-staked (- (var-get total-staked) (get amount stake-data)))

                        ;; Remove stake
                        (map-delete stakes tx-sender)

                        (print {
                            type: "unstake-completed",
                            staker: tx-sender,
                            amount: (get amount stake-data)
                        })
                        (ok (get amount stake-data)))
                ERR_COOLDOWN_ACTIVE)
        ERR_NO_STAKE_FOUND))

;; Claim staking rewards
(define-public (claim-rewards)
    (match (map-get? stakes tx-sender)
        stake-data
            (let ((blocks-staked (- block-height (get last-claim-height stake-data)))
                  (reward-amount (calculate-rewards (get amount stake-data) blocks-staked)))
                (if (> reward-amount u0)
                    (begin
                        ;; Update last claim height
                        (map-set stakes tx-sender (merge stake-data {
                            last-claim-height: block-height
                        }))

                        ;; Mint reward tokens
                        (try! (as-contract (contract-call? CARBON_TOKEN_CONTRACT mint reward-amount tx-sender)))

                        ;; Update tracking
                        (var-set total-rewards-distributed (+ (var-get total-rewards-distributed) reward-amount))

                        (print {
                            type: "rewards-claimed",
                            staker: tx-sender,
                            reward-amount: reward-amount
                        })
                        (ok reward-amount))
                    (ok u0)))
        ERR_NO_STAKE_FOUND))

;; Distribute protocol fees to stakers
(define-public (distribute-protocol-fees (fee-amount uint))
    (begin
        ;; Only marketplace contract can call this
        (asserts! (is-eq contract-caller 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.marketplace) ERR_INVALID_AMOUNT)
        (var-set fee-pool (+ (var-get fee-pool) fee-amount))
        (ok true)))

;; Helper functions
(define-private (calculate-rewards (stake-amount uint) (blocks-staked uint))
    (let ((annual-blocks u52560) ;; Approximate blocks per year
          (reward-rate-per-block (/ REWARD_RATE annual-blocks)))
        (/ (* stake-amount blocks-staked reward-rate-per-block) u100)))

;; Read-only functions
(define-read-only (get-stake-info (staker principal))
    (map-get? stakes staker))

(define-read-only (calculate-pending-rewards (staker principal))
    (match (map-get? stakes staker)
        stake-data
            (let ((blocks-staked (- block-height (get last-claim-height stake-data))))
                (calculate-rewards (get amount stake-data) blocks-staked))
        u0))

(define-read-only (get-staking-stats)
    {
        total-staked: (var-get total-staked),
        total-rewards-distributed: (var-get total-rewards-distributed),
        fee-pool: (var-get fee-pool),
        min-stake-amount: MIN_STAKE_AMOUNT,
        cooldown-period: COOLDOWN_PERIOD
    })