;; Access control contract for GreenTrack platform
;; Manages roles and permissions for issuing credits and platform administration

;; Error constants
(define-constant ERR_UNAUTHORIZED (err u3001))
(define-constant ERR_ALREADY_ADMIN (err u3002))
(define-constant ERR_NOT_ADMIN (err u3003))
(define-constant ERR_ALREADY_ISSUER (err u3004))
(define-constant ERR_NOT_ISSUER (err u3005))

;; Contract owner
(define-constant CONTRACT_OWNER tx-sender)

;; Data maps
(define-map admins principal bool)
(define-map credit-issuers principal bool)
(define-map issuer-metadata principal {
    name: (string-ascii 64),
    verification-level: uint,
    credits-issued: uint
})

;; Initialize contract owner as admin
(map-set admins CONTRACT_OWNER true)

;; Admin management functions
(define-public (add-admin (new-admin principal))
    (begin
        (asserts! (is-admin tx-sender) ERR_UNAUTHORIZED)
        (asserts! (not (is-admin new-admin)) ERR_ALREADY_ADMIN)
        (ok (map-set admins new-admin true))))

(define-public (remove-admin (admin principal))
    (begin
        (asserts! (is-admin tx-sender) ERR_UNAUTHORIZED)
        (asserts! (not (is-eq admin CONTRACT_OWNER)) ERR_UNAUTHORIZED)
        (asserts! (is-admin admin) ERR_NOT_ADMIN)
        (ok (map-delete admins admin))))

;; Credit issuer management
(define-public (add-credit-issuer (issuer principal) (name (string-ascii 64)) (verification-level uint))
    (begin
        (asserts! (is-admin tx-sender) ERR_UNAUTHORIZED)
        (asserts! (not (is-credit-issuer issuer)) ERR_ALREADY_ISSUER)
        (map-set credit-issuers issuer true)
        (ok (map-set issuer-metadata issuer {
            name: name,
            verification-level: verification-level,
            credits-issued: u0
        }))))

(define-public (remove-credit-issuer (issuer principal))
    (begin
        (asserts! (is-admin tx-sender) ERR_UNAUTHORIZED)
        (asserts! (is-credit-issuer issuer) ERR_NOT_ISSUER)
        (map-delete credit-issuers issuer)
        (ok (map-delete issuer-metadata issuer))))

(define-public (update-issuer-credits-count (issuer principal) (new-count uint))
    (begin
        (asserts! (is-credit-issuer tx-sender) ERR_UNAUTHORIZED)
        (match (map-get? issuer-metadata issuer)
            issuer-data (ok (map-set issuer-metadata issuer
                (merge issuer-data { credits-issued: new-count })))
            ERR_NOT_ISSUER)))

;; Read-only functions
(define-read-only (is-admin (user principal))
    (default-to false (map-get? admins user)))

(define-read-only (is-credit-issuer (user principal))
    (default-to false (map-get? credit-issuers user)))

(define-read-only (get-issuer-info (issuer principal))
    (map-get? issuer-metadata issuer))

(define-read-only (is-authorized-issuer (user principal))
    (or (is-admin user) (is-credit-issuer user)))