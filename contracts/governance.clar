;; Governance contract for GreenTrack platform
;; Handles proposal creation, voting, and execution of platform upgrades

(use-trait carbon-token-trait 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.carbon-token.sip-010-trait)

;; Error constants
(define-constant ERR_PROPOSAL_NOT_FOUND (err u8001))
(define-constant ERR_VOTING_CLOSED (err u8002))
(define-constant ERR_ALREADY_VOTED (err u8003))
(define-constant ERR_INSUFFICIENT_TOKENS (err u8004))
(define-constant ERR_PROPOSAL_NOT_PASSED (err u8005))
(define-constant ERR_PROPOSAL_ALREADY_EXECUTED (err u8006))

;; Governance parameters
(define-constant MIN_PROPOSAL_THRESHOLD u100000000) ;; 100 CCT to create proposal
(define-constant VOTING_PERIOD u1008) ;; ~1 week in blocks
(define-constant QUORUM_THRESHOLD u20) ;; 20% of total supply
(define-constant PASS_THRESHOLD u51) ;; 51% majority

;; Proposal types
(define-constant PROPOSAL_TYPE_PARAMETER u1)
(define-constant PROPOSAL_TYPE_UPGRADE u2)
(define-constant PROPOSAL_TYPE_TREASURY u3)

;; Data structures
(define-map proposals uint {
    proposer: principal,
    title: (string-ascii 64),
    description: (string-ascii 256),
    proposal-type: uint,
    voting-start: uint,
    voting-end: uint,
    yes-votes: uint,
    no-votes: uint,
    executed: bool
})

(define-map votes { proposal-id: uint, voter: principal } {
    vote: bool,
    voting-power: uint
})

(define-map voter-power principal uint)

;; Data variables
(define-data-var next-proposal-id uint u1)
(define-data-var total-voting-power uint u0)

;; References
(define-constant CARBON_TOKEN_CONTRACT 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.carbon-token)
(define-constant REWARDS_CONTRACT 'SP2PABAF9FTAJYNFZH93XENAJ8FVY99RRM50D2JG9.rewards)

;; Create governance proposal
(define-public (create-proposal
    (title (string-ascii 64))
    (description (string-ascii 256))
    (proposal-type uint))
    (let ((proposal-id (var-get next-proposal-id))
          (proposer-balance (contract-call? CARBON_TOKEN_CONTRACT get-balance tx-sender)))
        (asserts! (>= proposer-balance MIN_PROPOSAL_THRESHOLD) ERR_INSUFFICIENT_TOKENS)

        (map-set proposals proposal-id {
            proposer: tx-sender,
            title: title,
            description: description,
            proposal-type: proposal-type,
            voting-start: block-height,
            voting-end: (+ block-height VOTING_PERIOD),
            yes-votes: u0,
            no-votes: u0,
            executed: false
        })

        (var-set next-proposal-id (+ proposal-id u1))

        (print {
            type: "proposal-created",
            proposal-id: proposal-id,
            proposer: tx-sender,
            title: title,
            voting-end: (+ block-height VOTING_PERIOD)
        })
        (ok proposal-id)))

;; Vote on proposal
(define-public (vote (proposal-id uint) (support bool))
    (match (map-get? proposals proposal-id)
        proposal-data
            (let ((voter-tokens (contract-call? CARBON_TOKEN_CONTRACT get-balance tx-sender)))
                ;; Validations
                (asserts! (<= block-height (get voting-end proposal-data)) ERR_VOTING_CLOSED)
                (asserts! (>= block-height (get voting-start proposal-data)) ERR_VOTING_CLOSED)
                (asserts! (is-none (map-get? votes { proposal-id: proposal-id, voter: tx-sender })) ERR_ALREADY_VOTED)
                (asserts! (> voter-tokens u0) ERR_INSUFFICIENT_TOKENS)

                ;; Record vote
                (map-set votes { proposal-id: proposal-id, voter: tx-sender } {
                    vote: support,
                    voting-power: voter-tokens
                })

                ;; Update vote tallies
                (if support
                    (map-set proposals proposal-id (merge proposal-data {
                        yes-votes: (+ (get yes-votes proposal-data) voter-tokens)
                    }))
                    (map-set proposals proposal-id (merge proposal-data {
                        no-votes: (+ (get no-votes proposal-data) voter-tokens)
                    })))

                (print {
                    type: "vote-cast",
                    proposal-id: proposal-id,
                    voter: tx-sender,
                    support: support,
                    voting-power: voter-tokens
                })
                (ok true))
        ERR_PROPOSAL_NOT_FOUND))

;; Execute passed proposal
(define-public (execute-proposal (proposal-id uint))
    (match (map-get? proposals proposal-id)
        proposal-data
            (let ((total-votes (+ (get yes-votes proposal-data) (get no-votes proposal-data)))
                  (total-supply (unwrap-panic (contract-call? CARBON_TOKEN_CONTRACT get-total-supply))))
                ;; Validations
                (asserts! (> block-height (get voting-end proposal-data)) ERR_VOTING_CLOSED)
                (asserts! (not (get executed proposal-data)) ERR_PROPOSAL_ALREADY_EXECUTED)

                ;; Check quorum and majority
                (asserts! (>= (* total-votes u100) (* total-supply QUORUM_THRESHOLD)) ERR_PROPOSAL_NOT_PASSED)
                (asserts! (>= (* (get yes-votes proposal-data) u100) (* total-votes PASS_THRESHOLD)) ERR_PROPOSAL_NOT_PASSED)

                ;; Mark as executed
                (map-set proposals proposal-id (merge proposal-data { executed: true }))

                (print {
                    type: "proposal-executed",
                    proposal-id: proposal-id,
                    yes-votes: (get yes-votes proposal-data),
                    no-votes: (get no-votes proposal-data)
                })
                (ok true))
        ERR_PROPOSAL_NOT_FOUND))

;; Read-only functions
(define-read-only (get-proposal (proposal-id uint))
    (map-get? proposals proposal-id))

(define-read-only (get-vote (proposal-id uint) (voter principal))
    (map-get? votes { proposal-id: proposal-id, voter: voter }))

(define-read-only (get-voting-power (voter principal))
    (contract-call? CARBON_TOKEN_CONTRACT get-balance voter))

(define-read-only (is-proposal-active (proposal-id uint))
    (match (map-get? proposals proposal-id)
        proposal-data (and (<= block-height (get voting-end proposal-data))
                          (>= block-height (get voting-start proposal-data)))
        false))

(define-read-only (get-governance-stats)
    {
        total-proposals: (- (var-get next-proposal-id) u1),
        min-proposal-threshold: MIN_PROPOSAL_THRESHOLD,
        voting-period: VOTING_PERIOD,
        quorum-threshold: QUORUM_THRESHOLD,
        pass-threshold: PASS_THRESHOLD
    })