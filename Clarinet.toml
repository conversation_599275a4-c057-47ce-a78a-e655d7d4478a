[project]
name = 'greentrack-carbon-exchange'
description = ''
authors = []
telemetry = true
cache_dir = './.cache'
requirements = []
[contracts.access-control]
path = 'contracts/access-control.clar'
clarity_version = 3
epoch = 3.1

[contracts.carbon-token]
path = 'contracts/carbon-token.clar'
clarity_version = 3
epoch = 3.1

[contracts.credit-registry]
path = 'contracts/credit-registry.clar'
clarity_version = 3
epoch = 3.1

[contracts.governance]
path = 'contracts/governance.clar'
clarity_version = 3
epoch = 3.1

[contracts.marketplace]
path = 'contracts/marketplace.clar'
clarity_version = 3
epoch = 3.1

[contracts.math]
path = 'contracts/math.clar'
clarity_version = 3
epoch = 3.1

[contracts.rewards]
path = 'contracts/rewards.clar'
clarity_version = 3
epoch = 3.1

[contracts.staking]
path = 'contracts/staking.clar'
clarity_version = 3
epoch = 3.1

[contracts.strings]
path = 'contracts/strings.clar'
clarity_version = 3
epoch = 3.1
[repl.analysis]
passes = ['check_checker']

[repl.analysis.check_checker]
strict = false
trusted_sender = false
trusted_caller = false
callee_filter = false
