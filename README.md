# GreenTrack Carbon Exchange

![Build Status](https://img.shields.io/github/actions/workflow/status/greentrack-carbon-exchange/ci.yml?branch=main\&style=flat-square)
![Stability: Experimental](https://img.shields.io/badge/stability-experimental-yellow?style=flat-square)

A decentralized carbon credit platform powered by Stacks & Clarity. GreenTrack enables verified issuers to mint on-chain carbon credits, users to trade and stake tokens for rewards, and token holders to govern protocol parameters—all transparently and without intermediaries.

---

## 📖 Table of Contents

1. [Introduction](#introduction)
2. [Key Features](#key-features)
3. [Tech Stack](#tech-stack)
4. [Smart Contract Modules](#smart-contract-modules)
5. [Folder Structure](#folder-structure)
6. [Prerequisites](#prerequisites)
7. [Installation & Setup](#installation--setup)
8. [Running Locally](#running-locally)
9. [Testing](#testing)
10. [Continuous Integration](#continuous-integration)
11. [Usage Examples](#usage-examples)
12. [Governance & Contributions](#governance--contributions)
13. [License](#license)
14. [Acknowledgments](#acknowledgments)

---

## 📝 Introduction

GreenTrack Carbon Exchange is a next-generation decentralized application (DApp) on Stacks, designed to bring transparency and efficiency to the carbon credit market. It marries the security of on-chain contracts with a modular, community-driven governance model to manage issuance, trading, staking, and evolution of carbon credits.

Organizations can prove and monetize their carbon offset projects, while individuals and enterprises can buy, hold, and trade verifiable credits in an open marketplace. Participants earn rewards through staking and shape the protocol’s future by participating in on-chain governance.

---

## 🚀 Key Features

* **On-Chain Issuance & Registry**: Only approved issuers can mint carbon credits, each tracked with metadata (issuer ID, project details, issuance date).
* **CarbonToken (Fungible Token)**: ERC-20‑style token representing 1 ton of CO₂ offset. Supports minting, transfers, and burns on redemption.
* **Decentralized Marketplace**: List and trade credits in STX or stablecoins with atomic escrow for trustless settlement.
* **Staking & Rewards**: Stake CarbonTokens to earn `GreenPoints` (governance tokens) and protocol fee revenue.
* **Governance Module**: Submit and vote on protocol upgrades, parameter changes, and new issuer approvals via on-chain proposals.
* **Role-Based Access Control**: Manage issuer onboarding, pausing operations, and admin tasks securely through an `access-control` contract.
* **Modular Architecture**: Clean separation of concerns across at least seven core smart-contract modules and utility libraries.
* **Comprehensive Test Suite**: Automated tests cover token behavior, registry operations, marketplace trades, staking, and governance workflows.
* **CI & CI/CD Integration**: GitHub Actions workflow runs `clarinet check` & `clarinet test` on every push, ensuring code quality and reliability.

---

## 🛠️ Tech Stack

* **Clarity**: Smart-contract language for Stacks blockchain.
* **Clarinet**: Local development, testing, and deployment framework.
* **GitHub Actions**: Continuous integration & testing.
* **TypeScript / JavaScript** (optional): For future front-end integration.

---

## 🔗 Smart Contract Modules

| Contract File          | Responsibilities                                    |
| ---------------------- | --------------------------------------------------- |
| `carbon-token.clar`    | Implements CarbonToken (mint, transfer, burn).      |
| `credit-registry.clar` | Manages issuer registry and credit metadata.        |
| `marketplace.clar`     | List, buy, and sell logic with escrow handling.     |
| `staking.clar`         | Stake/unstake logic and fee distribution.           |
| `governance.clar`      | Proposal creation, voting, and execution timelocks. |
| `access-control.clar`  | Role-based issuer/admin management.                 |
| `rewards.clar`         | GreenPoints minting and reward schedule.            |
| `utils/math.clar`      | Shared mathematical helper functions.               |
| `utils/strings.clar`   | Shared string and metadata utilities.               |

---

## 📂 Folder Structure

```
├── contracts/
│   ├── access-control.clar
│   ├── carbon-token.clar
│   ├── credit-registry.clar
│   ├── governance.clar
│   ├── marketplace.clar
│   ├── rewards.clar
│   ├── staking.clar
│   └── utils/
│       ├── math.clar
│       └── strings.clar
├── tests/
│   ├── test_carbon_token.clar
│   ├── test_registry.clar
│   ├── test_marketplace.clar
│   ├── test_staking.clar
│   └── test_governance.clar
├── .github/
│   └── workflows/
│       └── ci.yml
├── clarinet.toml
└── README.md
```

---

## 🔍 Prerequisites

* **Node.js 16+**
* **Clarinet** (`npm install -g @hirosystems/clarinet`)
* **Git** and **GitHub** account
* **Docker** (optional, for isolated testing)

---

## ⚙️ Installation & Setup

1. **Clone the repository**

   ```bash
   git clone https://github.com/your-username/greentrack-carbon-exchange.git
   cd greentrack-carbon-exchange
   ```

2. **Install dependencies** (if you plan to extend with JS/TS front end)

   ```bash
   npm install
   ```

3. **Initialize Clarinet** (if not already)

   ```bash
   clarinet init
   ```

4. **Configure network** (optional)

   * Edit `clarinet.toml` to adjust ports or connect to a testnet.

---

## ▶️ Running Locally

Start a local Stacks node and deploy contracts:

```bash
clarinet start
```

Run tests:

```bash
clarinet test
```

Compile & check contracts:

```bash
clarinet check
```

---

## 🧪 Testing

* Tests live in the `tests/` directory following Clarinet conventions.
* Key test cases include:

  * **Token Behavior**: Mint, transfer, and burn flows.
  * **Registry Operations**: Register issuer, list credits, metadata lookups.
  * **Marketplace Trades**: Listing, buying, fund escrow release.
  * **Staking**: Stake/unstake and reward distribution.
  * **Governance**: Proposal lifecycle and vote tallying.

To add tests, create new `.clar` files in `tests/` and follow existing patterns.

---

## 🔄 Continuous Integration

A GitHub Actions workflow (`.github/workflows/ci.yml`) is configured to:

1. Install Clarinet.
2. Run `clarinet check`.
3. Run `clarinet test`.

All pull requests must pass CI before merging.

---

## 💡 Usage Examples

1. **Mint Carbon Credits**

   ```bash
   clarinet run contract carbon-token mint <amount> --sender <issuer-address>
   ```

2. **List Credits for Sale**

   ```bash
   clarinet run contract marketplace list <credit-id> <price> --sender <user>
   ```

3. **Stake Credits**

   ```bash
   clarinet run contract staking stake <token-amount> --sender <user>
   ```

4. **Submit Governance Proposal**

   ```bash
   clarinet run contract governance propose '<proposal-json>' --sender <governor-address>
   ```

---

## 🤝 Governance & Contributions

* **Governance**: Hold `GreenPoints` to propose and vote on changes. Quorum and voting periods are defined in `governance.clar`.
* **Contributions**:

  1. Fork the repo and create a feature branch.
  2. Implement your feature or fix.
  3. Add tests for new functionality.
  4. Submit a pull request with a descriptive title and body.

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for detailed guidelines.

---

## 📄 License

This project is licensed under the **MIT License**. See [LICENSE](LICENSE) for details.

---

## 🙏 Acknowledgments

* Built with [Stacks](https://stacks.co/) and [Clarity](https://docs.stacks.co/docs/clarity/overview).
* Inspired by best practices from open‑source DeFi and carbon marketplaces.
